// Note Menu Functionality with jQuery
$(document).ready(function() {
  console.log('[DEBUG] Document ready - initializing note menu with jQuery');
  initializeNoteMenus();
  setupUpdateNoteModal();
});

// Initialize all note menu buttons
function initializeNoteMenus() {
  console.log('[DEBUG] Initializing note menus with jQuery');
  
  // First, ensure all menus start hidden
  $('.note-menu').addClass('hidden');
  
  // Handle menu button clicks using event delegation
  $(document).off('click', '.note-menu-button').on('click', '.note-menu-button', function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    const $button = $(this);
    const noteId = $button.data('note-id');
    console.log('[DEBUG] Menu button clicked for note ID:', noteId);
    
    // Find the corresponding menu
    const $menu = $(`.note-menu[data-note-id="${noteId}"]`);
    
    if ($menu.length) {
      // First hide all other menus
      $('.note-menu').not($menu).addClass('hidden');
      
      // Check if this menu is currently hidden
      const isHidden = $menu.hasClass('hidden');
      console.log('[DEBUG] Menu is currently hidden:', isHidden);
      
      if (isHidden) {
        // Show this menu
        $menu.removeClass('hidden');
        console.log('[DEBUG] Menu should now be visible');

        // Check if menu is actually visible after removing hidden class
        setTimeout(() => {
          console.log('[DEBUG] Menu hidden class after removal:', $menu.hasClass('hidden'));
          console.log('[DEBUG] Menu computed display:', $menu.css('display'));
          console.log('[DEBUG] Menu computed visibility:', $menu.css('visibility'));
          console.log('[DEBUG] Menu computed opacity:', $menu.css('opacity'));
        }, 10);

        // Position the menu
        positionMenu($menu, $button);
      } else {
        // Hide this menu
        $menu.addClass('hidden');
        console.log('[DEBUG] Menu hidden');
      }
    } else {
      console.error('[DEBUG] No menu found with data-note-id:', noteId);
    }
  });
  
  // Close menus when clicking outside
  $(document).on('click', function(e) {
    if (!$(e.target).closest('.note-menu, .note-menu-button').length) {
      $('.note-menu').addClass('hidden');
      console.log('[DEBUG] Clicked outside, hiding all menus');
    }
  });
  
  // Temporarily disabled AJAX complete handler to debug menu visibility issues
  // $(document).ajaxComplete(function(event, xhr, settings) {
  //   // Check if the response contains notes (either patient notes or calendar booking notes)
  //   if (settings.url.includes('/notes') || settings.url.includes('/calendar_bookings')) {
  //     console.log('[DEBUG] AJAX completed, reinitializing menus for new content');
  //     // Ensure all menus start hidden
  //     $('.note-menu').addClass('hidden');
  //   }
  // });
}

// Function to position the menu relative to the button
function positionMenu($menu, $button) {
  // Reset any previously set positioning
  $menu.css({
    top: '',
    left: '',
    right: '',
    bottom: '',
    marginTop: '',
    marginBottom: ''
  });
  
  // Get the button's position
  const buttonPos = $button.offset();
  const buttonHeight = $button.outerHeight();
  const buttonWidth = $button.outerWidth();
  const menuHeight = $menu.outerHeight();
  const viewportHeight = $(window).height();
  const scrollTop = $(window).scrollTop();
  
  // Calculate space below the button
  const buttonBottom = buttonPos.top + buttonHeight;
  const spaceBelow = viewportHeight - (buttonBottom - scrollTop);
  
  console.log('[DEBUG] Menu positioning - button position:', buttonPos);
  console.log('[DEBUG] Menu positioning - menu height:', menuHeight);
  console.log('[DEBUG] Menu positioning - space below:', spaceBelow);
  
  // Position the menu
  if (spaceBelow < menuHeight + 10) {
    // Not enough space below, position above
    $menu.css({
      bottom: '100%',
      top: 'auto',
      right: '0',
      marginBottom: '5px'
    });
    console.log('[DEBUG] Menu positioned above button');
  } else {
    // Enough space below, position below
    $menu.css({
      top: '100%',
      bottom: 'auto',
      right: '0',
      marginTop: '5px'
    });
    console.log('[DEBUG] Menu positioned below button');
  }
}

// Function to set up the update note modal
function setupUpdateNoteModal() {
  const modal = document.getElementById('update-note-modal');
  console.log('[DEBUG] Setting up update note modal, found:', modal);
  if (!modal) {
    console.error('[DEBUG] Update note modal not found in the DOM');
    return;
  }
  
  // Close modal buttons
  const closeModalBtn = document.getElementById('close-update-note-modal');
  const cancelBtn = document.getElementById('cancel-update-note');
  
  // Close modal function
  const closeModal = function() {
    modal.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    console.log('[DEBUG] Modal closed');
  };
  
  // Close with X button
  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', closeModal);
    console.log('[DEBUG] Close button event listener added');
  }
  
  // Close with Cancel button
  if (cancelBtn) {
    cancelBtn.addEventListener('click', closeModal);
    console.log('[DEBUG] Cancel button event listener added');
  }
  
  // Close when clicking outside modal
  modal.addEventListener('click', function(event) {
    if (event.target === modal) {
      closeModal();
    }
  });
  
  // Handle color selection
  const colorOptions = document.querySelectorAll('.note-color-option');
  const colorInput = document.getElementById('update_note_color');
  console.log('[DEBUG] Color options found:', colorOptions.length);
  
  colorOptions.forEach(option => {
    option.addEventListener('click', function() {
      // Remove selected class from all options
      colorOptions.forEach(opt => opt.classList.remove('ring-2', 'ring-offset-2'));
      
      // Add selected class to clicked option
      this.classList.add('ring-2', 'ring-offset-2');
      
      // Set the color value in the hidden input
      if (colorInput) {
        colorInput.value = this.dataset.color;
        console.log('[DEBUG] Color selected:', this.dataset.color);
      }
    });
  });
  
  // Handle form submission
  const form = document.getElementById('update_patient_note_form');
  if (form) {
    console.log('[DEBUG] Form found, adding submit handler');
    form.addEventListener('submit', function(event) {
      event.preventDefault();
      console.log('[DEBUG] Form submitted');
      
      const noteId = document.getElementById('update_note_id').value;
      const title = form.querySelector('input[name="patient_note[title]"]').value;
      const text = form.querySelector('textarea[name="patient_note[text]"]').value;
      const color = form.querySelector('input[name="patient_note[color]"]').value;
      
      console.log('[DEBUG] Submitting note update:', { noteId, title, text, color });
      
      // Show loading state on button
      const saveBtn = document.getElementById('save-update-note');
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> Updating...';
      
      // Make AJAX request to update the note
      $.ajax({
        url: `/admin/patient_notes/${noteId}`,
        method: 'PATCH',
        data: {
          patient_note: {
            title: title,
            text: text,
            color: color
          },
          authenticity_token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          console.log('[DEBUG] Note updated successfully:', response);
          // Show success message
          toastr.success('Note updated successfully');
          
          // Update the note in the UI
          if (response.html) {
            const noteElement = document.querySelector(`.note[data-note-id="${noteId}"]`);
            if (noteElement) {
              noteElement.outerHTML = response.html;
              console.log('[DEBUG] Note HTML updated in the DOM');
            }
          }
          
          // Close modal
          closeModal();
          
          // Reinitialize menu buttons for the new note
          const newMenuButton = document.querySelector(`.note-menu-button[data-note-id="${noteId}"]`);
          if (newMenuButton) {
            newMenuButton.addEventListener('click', handleMenuButtonClick);
            console.log('[DEBUG] Reinitialized menu button for updated note');
          }
        },
        error: function(xhr) {
          console.error('[DEBUG] Error updating note:', xhr);
          // Show error message
          toastr.error(xhr.responseJSON?.error || 'Failed to update note');
        },
        complete: function() {
          // Reset button state
          saveBtn.disabled = false;
          saveBtn.innerHTML = 'Update Note';
          console.log('[DEBUG] Update button reset');
        }
      });
    });
  } else {
    console.error('[DEBUG] Update note form not found');
  }
}

// Function to open the update note modal - make it globally accessible
window.openUpdateNoteModal = function(event, noteId) {
  console.log('[DEBUG] openUpdateNoteModal called with noteId:', noteId);
  event.preventDefault();
  
  // Close any open menus
  document.querySelectorAll('.note-menu').forEach(menu => {
    menu.classList.add('hidden');
  });
  
  // Find the modal
  const modal = document.getElementById('update-note-modal');
  console.log('[DEBUG] Modal element found:', modal);
  if (!modal) {
    console.error('[DEBUG] Modal not found: update-note-modal');
    alert('Debug: Modal not found: update-note-modal');
    return;
  }
  
  // Show the modal
  modal.classList.remove('hidden');
  document.body.classList.add('overflow-hidden');
  console.log('[DEBUG] Modal should now be visible');
  
  // Log modal state after a short delay
  setTimeout(() => {
    console.log('[DEBUG] Modal hidden class present:', modal.classList.contains('hidden'));
    console.log('[DEBUG] Modal computed style display:', window.getComputedStyle(modal).display);
    console.log('[DEBUG] Modal z-index:', window.getComputedStyle(modal).zIndex);
    console.log('[DEBUG] Modal position:', window.getComputedStyle(modal).position);
  }, 100);
  
  // Set the note ID in the form
  const noteIdInput = document.getElementById('update_note_id');
  console.log('[DEBUG] Note ID input found:', noteIdInput);
  if (noteIdInput) {
    noteIdInput.value = noteId;
    console.log('[DEBUG] Set note ID in form to:', noteId);
  } else {
    console.error('[DEBUG] Note ID input not found');
    alert('Debug: Note ID input not found');
  }
  
  // Fetch the note data to pre-fill the form
  console.log('[DEBUG] Fetching note data from:', `/admin/patient_notes/${noteId}/edit`);
  fetch(`/admin/patient_notes/${noteId}/edit`, {
    headers: {
      'Accept': 'application/json'
    }
  })
  .then(response => {
    console.log('[DEBUG] Fetch response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('[DEBUG] Note data received:', data);
    // Pre-fill the form with the note data
    const titleInput = document.querySelector('#update_patient_note_form input[name="patient_note[title]"]');
    const textInput = document.querySelector('#update_patient_note_form textarea[name="patient_note[text]"]');
    const colorInput = document.getElementById('update_note_color');
    
    console.log('[DEBUG] Form inputs found:', { titleInput, textInput, colorInput });
    
    if (titleInput) titleInput.value = data.title || '';
    if (textInput) textInput.value = data.text || '';
    if (colorInput) colorInput.value = data.color || '';
    
    // Select the correct color option
    const colorOptions = document.querySelectorAll('.note-color-option');
    console.log('[DEBUG] Color options found:', colorOptions.length);
    colorOptions.forEach(option => {
      option.classList.remove('ring-2', 'ring-offset-2');
      if (option.dataset.color === data.color) {
        option.classList.add('ring-2', 'ring-offset-2');
        console.log('[DEBUG] Selected color option:', option.dataset.color);
      }
    });
  })
  .catch(error => {
    console.error('[DEBUG] Error fetching note data:', error);
    alert('Debug: Error loading note data: ' + error.message);
    toastr.error('Failed to load note data');
  });
};
