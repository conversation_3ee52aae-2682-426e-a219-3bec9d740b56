// Note Menu Functionality with jQuery
$(document).ready(function() {
  console.log('[DEBUG] Document ready - initializing note menu with jQuery');
  initializeNoteMenus();
  setupUpdateNoteModal();
});

// Initialize all note menu buttons
function initializeNoteMenus() {
  console.log('[DEBUG] Initializing note menus with jQuery');
  
  // First, ensure all menus start hidden
  $('.note-menu').addClass('hidden');
  
  // Handle menu button clicks using event delegation
  $(document).off('click', '.note-menu-button').on('click', '.note-menu-button', function(e) {
    e.preventDefault();
    e.stopPropagation();

    const $button = $(this);
    const noteId = $button.data('note-id');
    console.log('[DEBUG] ========== MENU BUTTON CLICKED ==========');
    console.log('[DEBUG] Menu button clicked for note ID:', noteId);
    console.log('[DEBUG] Button element:', $button[0]);

    // Find the corresponding menu
    const $menu = $(`.note-menu[data-note-id="${noteId}"]`);
    console.log('[DEBUG] Found menu elements:', $menu.length);
    console.log('[DEBUG] Menu element:', $menu[0]);

    if ($menu.length) {
      // Log initial state
      console.log('[DEBUG] Initial menu classes:', $menu[0].className);
      console.log('[DEBUG] Initial menu style:', $menu[0].style.cssText);

      // First hide all other menus
      const $otherMenus = $('.note-menu').not($menu);
      console.log('[DEBUG] Hiding other menus count:', $otherMenus.length);
      $otherMenus.addClass('hidden');

      // Check if this menu is currently hidden
      const isHidden = $menu.hasClass('hidden');
      console.log('[DEBUG] Menu is currently hidden:', isHidden);

      if (isHidden) {
        console.log('[DEBUG] Attempting to show menu...');

        // Show this menu
        $menu.removeClass('hidden');
        console.log('[DEBUG] Removed hidden class');
        console.log('[DEBUG] Menu classes after removal:', $menu[0].className);

        // Force display block to override any CSS
        $menu.css('display', 'block');
        console.log('[DEBUG] Forced display block');
        console.log('[DEBUG] Menu style after forcing display:', $menu[0].style.cssText);

        // Check computed styles immediately
        const computedStyle = window.getComputedStyle($menu[0]);
        console.log('[DEBUG] Computed display:', computedStyle.display);
        console.log('[DEBUG] Computed visibility:', computedStyle.visibility);
        console.log('[DEBUG] Computed opacity:', computedStyle.opacity);
        console.log('[DEBUG] Computed z-index:', computedStyle.zIndex);
        console.log('[DEBUG] Computed position:', computedStyle.position);

        // Position the menu
        console.log('[DEBUG] Positioning menu...');
        positionMenu($menu, $button);

        // Final check after positioning
        setTimeout(() => {
          console.log('[DEBUG] ===== FINAL CHECK (50ms) =====');
          console.log('[DEBUG] Final menu hidden class:', $menu.hasClass('hidden'));
          console.log('[DEBUG] Final menu classes:', $menu[0].className);
          console.log('[DEBUG] Final menu style:', $menu[0].style.cssText);

          const finalComputedStyle = window.getComputedStyle($menu[0]);
          console.log('[DEBUG] Final computed display:', finalComputedStyle.display);
          console.log('[DEBUG] Final computed visibility:', finalComputedStyle.visibility);
          console.log('[DEBUG] Final computed opacity:', finalComputedStyle.opacity);
          console.log('[DEBUG] ========================================');
        }, 50);

      } else {
        // Hide this menu
        console.log('[DEBUG] Hiding menu...');
        $menu.addClass('hidden');
        console.log('[DEBUG] Menu hidden');
      }
    } else {
      console.error('[DEBUG] No menu found with data-note-id:', noteId);
      console.log('[DEBUG] Available menus:', $('.note-menu').map(function() { return $(this).data('note-id'); }).get());
    }
  });
  
  // Close menus when clicking outside
  $(document).on('click', function(e) {
    console.log('[DEBUG] Document click detected');
    console.log('[DEBUG] Click target:', e.target);
    console.log('[DEBUG] Closest note-menu:', $(e.target).closest('.note-menu').length);
    console.log('[DEBUG] Closest note-menu-button:', $(e.target).closest('.note-menu-button').length);

    if (!$(e.target).closest('.note-menu, .note-menu-button').length) {
      console.log('[DEBUG] Clicked outside, hiding all menus');
      $('.note-menu').addClass('hidden');
    } else {
      console.log('[DEBUG] Clicked inside menu area, not hiding');
    }
  });
  
  // Temporarily disabled AJAX complete handler to debug menu visibility issues
  // $(document).ajaxComplete(function(event, xhr, settings) {
  //   // Check if the response contains notes (either patient notes or calendar booking notes)
  //   if (settings.url.includes('/notes') || settings.url.includes('/calendar_bookings')) {
  //     console.log('[DEBUG] AJAX completed, reinitializing menus for new content');
  //     // Ensure all menus start hidden
  //     $('.note-menu').addClass('hidden');
  //   }
  // });
}

// Function to position the menu relative to the button
function positionMenu($menu, $button) {
  console.log('[DEBUG] ===== POSITIONING MENU =====');
  console.log('[DEBUG] Menu before positioning - classes:', $menu[0].className);
  console.log('[DEBUG] Menu before positioning - style:', $menu[0].style.cssText);

  // Reset any previously set positioning
  $menu.css({
    top: '',
    left: '',
    right: '',
    bottom: '',
    marginTop: '',
    marginBottom: ''
  });

  console.log('[DEBUG] Menu after reset - style:', $menu[0].style.cssText);

  // Get the button's position
  const buttonPos = $button.offset();
  const buttonHeight = $button.outerHeight();
  const buttonWidth = $button.outerWidth();
  const menuHeight = $menu.outerHeight();
  const viewportHeight = $(window).height();
  const scrollTop = $(window).scrollTop();

  // Calculate space below the button
  const buttonBottom = buttonPos.top + buttonHeight;
  const spaceBelow = viewportHeight - (buttonBottom - scrollTop);

  console.log('[DEBUG] Menu positioning - button position:', buttonPos);
  console.log('[DEBUG] Menu positioning - menu height:', menuHeight);
  console.log('[DEBUG] Menu positioning - space below:', spaceBelow);

  // Position the menu
  if (spaceBelow < menuHeight + 10) {
    // Not enough space below, position above
    $menu.css({
      bottom: '100%',
      top: 'auto',
      right: '0',
      marginBottom: '5px'
    });
    console.log('[DEBUG] Menu positioned above button');
  } else {
    // Enough space below, position below
    $menu.css({
      top: '100%',
      bottom: 'auto',
      right: '0',
      marginTop: '5px'
    });
    console.log('[DEBUG] Menu positioned below button');
  }

  console.log('[DEBUG] Menu after positioning - style:', $menu[0].style.cssText);
  console.log('[DEBUG] Menu after positioning - classes:', $menu[0].className);

  // Check if positioning broke the display
  const postPositionStyle = window.getComputedStyle($menu[0]);
  console.log('[DEBUG] Post-position computed display:', postPositionStyle.display);
  console.log('[DEBUG] Post-position computed visibility:', postPositionStyle.visibility);
  console.log('[DEBUG] ===== END POSITIONING =====');
}

// Function to set up the update note modal
function setupUpdateNoteModal() {
  const modal = document.getElementById('update-note-modal');
  console.log('[DEBUG] Setting up update note modal, found:', modal);
  if (!modal) {
    console.error('[DEBUG] Update note modal not found in the DOM');
    return;
  }
  
  // Close modal buttons
  const closeModalBtn = document.getElementById('close-update-note-modal');
  const cancelBtn = document.getElementById('cancel-update-note');
  
  // Close modal function
  const closeModal = function() {
    modal.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    console.log('[DEBUG] Modal closed');
  };
  
  // Close with X button
  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', closeModal);
    console.log('[DEBUG] Close button event listener added');
  }
  
  // Close with Cancel button
  if (cancelBtn) {
    cancelBtn.addEventListener('click', closeModal);
    console.log('[DEBUG] Cancel button event listener added');
  }
  
  // Close when clicking outside modal
  modal.addEventListener('click', function(event) {
    if (event.target === modal) {
      closeModal();
    }
  });
  
  // Handle color selection
  const colorOptions = document.querySelectorAll('.note-color-option');
  const colorInput = document.getElementById('update_note_color');
  console.log('[DEBUG] Color options found:', colorOptions.length);
  
  colorOptions.forEach(option => {
    option.addEventListener('click', function() {
      // Remove selected class from all options
      colorOptions.forEach(opt => opt.classList.remove('ring-2', 'ring-offset-2'));
      
      // Add selected class to clicked option
      this.classList.add('ring-2', 'ring-offset-2');
      
      // Set the color value in the hidden input
      if (colorInput) {
        colorInput.value = this.dataset.color;
        console.log('[DEBUG] Color selected:', this.dataset.color);
      }
    });
  });
  
  // Handle form submission
  const form = document.getElementById('update_patient_note_form');
  if (form) {
    console.log('[DEBUG] Form found, adding submit handler');
    form.addEventListener('submit', function(event) {
      event.preventDefault();
      console.log('[DEBUG] Form submitted');
      
      const noteId = document.getElementById('update_note_id').value;
      const title = form.querySelector('input[name="patient_note[title]"]').value;
      const text = form.querySelector('textarea[name="patient_note[text]"]').value;
      const color = form.querySelector('input[name="patient_note[color]"]').value;
      
      console.log('[DEBUG] Submitting note update:', { noteId, title, text, color });
      
      // Show loading state on button
      const saveBtn = document.getElementById('save-update-note');
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="inline-block animate-spin mr-2">↻</span> Updating...';
      
      // Make AJAX request to update the note
      $.ajax({
        url: `/admin/patient_notes/${noteId}`,
        method: 'PATCH',
        data: {
          patient_note: {
            title: title,
            text: text,
            color: color
          },
          authenticity_token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
          console.log('[DEBUG] Note updated successfully:', response);
          // Show success message
          toastr.success('Note updated successfully');
          
          // Update the note in the UI
          if (response.html) {
            const noteElement = document.querySelector(`.note[data-note-id="${noteId}"]`);
            if (noteElement) {
              noteElement.outerHTML = response.html;
              console.log('[DEBUG] Note HTML updated in the DOM');
            }
          }
          
          // Close modal
          closeModal();
          
          // Reinitialize menu buttons for the new note
          const newMenuButton = document.querySelector(`.note-menu-button[data-note-id="${noteId}"]`);
          if (newMenuButton) {
            newMenuButton.addEventListener('click', handleMenuButtonClick);
            console.log('[DEBUG] Reinitialized menu button for updated note');
          }
        },
        error: function(xhr) {
          console.error('[DEBUG] Error updating note:', xhr);
          // Show error message
          toastr.error(xhr.responseJSON?.error || 'Failed to update note');
        },
        complete: function() {
          // Reset button state
          saveBtn.disabled = false;
          saveBtn.innerHTML = 'Update Note';
          console.log('[DEBUG] Update button reset');
        }
      });
    });
  } else {
    console.error('[DEBUG] Update note form not found');
  }
}

// Function to open the update note modal - make it globally accessible
window.openUpdateNoteModal = function(event, noteId) {
  console.log('[DEBUG] openUpdateNoteModal called with noteId:', noteId);
  event.preventDefault();
  
  // Close any open menus
  document.querySelectorAll('.note-menu').forEach(menu => {
    menu.classList.add('hidden');
  });
  
  // Find the modal
  const modal = document.getElementById('update-note-modal');
  console.log('[DEBUG] Modal element found:', modal);
  if (!modal) {
    console.error('[DEBUG] Modal not found: update-note-modal');
    alert('Debug: Modal not found: update-note-modal');
    return;
  }
  
  // Show the modal
  modal.classList.remove('hidden');
  document.body.classList.add('overflow-hidden');
  console.log('[DEBUG] Modal should now be visible');
  
  // Log modal state after a short delay
  setTimeout(() => {
    console.log('[DEBUG] Modal hidden class present:', modal.classList.contains('hidden'));
    console.log('[DEBUG] Modal computed style display:', window.getComputedStyle(modal).display);
    console.log('[DEBUG] Modal z-index:', window.getComputedStyle(modal).zIndex);
    console.log('[DEBUG] Modal position:', window.getComputedStyle(modal).position);
  }, 100);
  
  // Set the note ID in the form
  const noteIdInput = document.getElementById('update_note_id');
  console.log('[DEBUG] Note ID input found:', noteIdInput);
  if (noteIdInput) {
    noteIdInput.value = noteId;
    console.log('[DEBUG] Set note ID in form to:', noteId);
  } else {
    console.error('[DEBUG] Note ID input not found');
    alert('Debug: Note ID input not found');
  }
  
  // Fetch the note data to pre-fill the form
  console.log('[DEBUG] Fetching note data from:', `/admin/patient_notes/${noteId}/edit`);
  fetch(`/admin/patient_notes/${noteId}/edit`, {
    headers: {
      'Accept': 'application/json'
    }
  })
  .then(response => {
    console.log('[DEBUG] Fetch response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('[DEBUG] Note data received:', data);
    // Pre-fill the form with the note data
    const titleInput = document.querySelector('#update_patient_note_form input[name="patient_note[title]"]');
    const textInput = document.querySelector('#update_patient_note_form textarea[name="patient_note[text]"]');
    const colorInput = document.getElementById('update_note_color');
    
    console.log('[DEBUG] Form inputs found:', { titleInput, textInput, colorInput });
    
    if (titleInput) titleInput.value = data.title || '';
    if (textInput) textInput.value = data.text || '';
    if (colorInput) colorInput.value = data.color || '';
    
    // Select the correct color option
    const colorOptions = document.querySelectorAll('.note-color-option');
    console.log('[DEBUG] Color options found:', colorOptions.length);
    colorOptions.forEach(option => {
      option.classList.remove('ring-2', 'ring-offset-2');
      if (option.dataset.color === data.color) {
        option.classList.add('ring-2', 'ring-offset-2');
        console.log('[DEBUG] Selected color option:', option.dataset.color);
      }
    });
  })
  .catch(error => {
    console.error('[DEBUG] Error fetching note data:', error);
    alert('Debug: Error loading note data: ' + error.message);
    toastr.error('Failed to load note data');
  });
};
